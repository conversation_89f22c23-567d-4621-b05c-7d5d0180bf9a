import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import tailwindcss from '@tailwindcss/vite';
import { fileURLToPath } from 'url';

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue(), tailwindcss()],
  define: { 'process.env': {} },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  build: {
    chunkSizeWarningLimit: 5000,
  },
  // server: {
  //   allowedHosts: ['rnatz-2404-7c80-54-ea8e-fc4c-9cb1-a004-7cf0.a.free.pinggy.link'],
  // },
});
