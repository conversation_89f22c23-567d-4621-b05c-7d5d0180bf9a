import { createRouter, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/pages/LandingPage.vue'),
    meta: {
      title: 'ConnectRx - Personalized, High-Quality Treatments Shipped Directly to Your Doorstep',
    },
  },
  {
    path: '/product-list/:category_slug?',
    name: 'ProductList',
    component: () => import('@/pages/ProductList.vue'),
    meta: {
      title: 'ConnectRx - Personalized, High-Quality Treatments Shipped Directly to Your Doorstep',
    },
  },
  {
    path: '/product/:slug',
    name: 'Product',
    component: () => import('@/pages/ProductPage.vue'),
    meta: {
      title: 'ConnectRx - Personalized, High-Quality Treatments Shipped Directly to Your Doorstep',
    },
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else if (to.hash) {
      return { el: to.hash, behavior: 'smooth' };
    } else if (from.path === to.path) {
      return { top: 0 };
    } else {
      return { top: 0 };
    }
  },
});

router.beforeEach((to, from, next) => {
  document.title = to.meta.title;
  next();
});

export default router;
