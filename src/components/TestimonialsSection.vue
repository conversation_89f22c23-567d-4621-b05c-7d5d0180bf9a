<script setup>
const testimonials = [
  {
    name: "<PERSON>",
    location: "New York, NY",
    review:
      "Outstanding service and product quality. The delivery was incredibly fast and the customer support team went above and beyond to help me find the right supplements.",
  },
  {
    name: "<PERSON>",
    location: "Los Angeles, CA",
    review:
      "I have been a loyal customer for over two years. The products are authentic, reasonably priced, and the website makes ordering so convenient.",
  },
  {
    name: "<PERSON>",
    location: "Chicago, IL",
    review:
      "Exceptional experience from start to finish. The user-friendly interface and seamless checkout process make this my go-to healthcare provider.",
  },
];
</script>

<template>
  <!-- Testimonials -->
  <section class="py-24 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-20">
        <h2 class="text-4xl lg:text-5xl font-bold text-neutral-900 mb-6">
          Customer Stories
        </h2>
        <p class="text-xl text-neutral-600">
          Real experiences from our valued customers
        </p>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div v-for="(testimonial, index) in testimonials" :key="testimonial.name"
          class="bg-neutral-50 p-8 rounded-3xl border border-neutral-200">
          <div class="flex items-center mb-6">
            <div v-for="i in 5" :key="i" class="text-yellow-400 mr-1">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path
                  d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z">
                </path>
              </svg>
            </div>
          </div>
          <p class="text-neutral-700 mb-8 leading-relaxed text-lg">
            "{{ testimonial.review }}"
          </p>
          <div class="flex items-center">
            <img :src="`https://picsum.photos/48/48?random=${index + 20}`" :alt="testimonial.name"
              class="w-12 h-12 rounded-full mr-4" />
            <div>
              <p class="font-semibold text-neutral-900">
                {{ testimonial.name }}
              </p>
              <p class="text-sm text-neutral-500">
                {{ testimonial.location }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
