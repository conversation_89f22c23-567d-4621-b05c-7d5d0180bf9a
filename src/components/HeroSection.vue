<script setup>
import {
  IconArrowRight,
  IconPills,
  IconTruckDelivery,
} from "@tabler/icons-vue";
import { onMounted, onUnmounted, ref } from "vue";

const phrases = ["Losing weight.", "Better sex.", "Fuller hair."];
const currentPhrase = ref(phrases[0]);

let index = 0;
let intervalId;

onMounted(() => {
  intervalId = setInterval(() => {
    index = (index + 1) % phrases.length;
    currentPhrase.value = phrases[index];
  }, 2500); // change every 2.5 seconds
});

onUnmounted(() => {
  clearInterval(intervalId);
});
</script>

<template>
  <!-- Hero -->
  <section class="relative py-12 bg-white sm:py-16 lg:py-20 overflow-hidden">
    <!-- Animated background accent -->
    <div
      class="absolute -top-40 left-1/2 -translate-x-1/2 w-[700px] h-[700px] bg-amber-200/50 rounded-full blur-3xl animate-pulse-slow z-0">
    </div>
    <div class="absolute inset-0 z-0">
      <img class="object-cover w-full h-full" src="@/assets/images/grid-pattern.png" alt="" />
    </div>

    <!-- Hero content -->
    <div class="relative px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl z-10">
      <div class="max-w-5xl mx-auto text-center px-4">
        <h1 class="text-3xl font-medium text-gray-900 sm:text-5xl lg:text-7xl">
          <Transition name="slide-up" mode="out-in">
            <span :key="currentPhrase" class="text-amber-500 font-semibold block py-2">
              {{ currentPhrase }}
            </span>
          </Transition>
          <span class="ml-2">We've got you covered.</span>
        </h1>
        <p class="max-w-md mx-auto mt-6 text-base font-normal leading-7 text-gray-500">
          Personalized, high-quality treatments shipped directly to your doorstep.
        </p>

        <ul class="flex flex-col gap-2 sm:flex-row items-center justify-center mt-6 space-x-6 sm:space-x-8">
          <li class="flex items-center">
            <IconTruckDelivery class="w-5 h-5 mr-2 text-gray-400" />
            <span class="text-xs font-medium text-gray-900 sm:text-sm">
              Free shipping, if prescribed
            </span>
          </li>
          <li class="flex items-center">
            <IconPills class="w-5 h-5 mr-2 text-gray-400" />
            <span class="text-xs font-medium text-gray-900 sm:text-sm">
              Lab tested for quality and safety
            </span>
          </li>
        </ul>
      </div>
    </div>

    <!-- Treatment cards -->
    <div class="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-5 pb-8 mt-12 sm:mt-16 lg:mt-20 px-4">
      <!-- Weight Loss Card -->
      <a href="#"
        class="relative rounded-2xl min-h-[350px] sm:min-h-[400px] flex flex-col p-8 bg-gradient-to-br from-[#fef6e7] via-[#fdf3e6] to-[#fbeeea] overflow-hidden group hover:shadow-md transition-all duration-300 border border-amber-100">
        <h3 class="text-amber-700 text-2xl font-bold mb-4">Weight Loss</h3>
        <p class="text-amber-800 text-base mb-4 max-w-md">
          Lose up to 15% of your body weight with cutting-edge treatments.
        </p>
        <div class="relative flex-1">
          <img src="@/assets/images/wl-kit.png" alt="Weight Loss"
            class="w-32 h-32 object-contain select-none pointer-events-none absolute -left-2 bottom-6 lg:w-52 lg:h-52 lg:left-8 lg:bottom-8 group-hover:scale-110 transition-transform duration-300" />
        </div>
        <div
          class="absolute right-8 bottom-8 bg-black text-white font-semibold px-5 py-2.5 rounded-full shadow hover:bg-gray-800 transition-all flex items-center z-10">
          Get Started
          <IconArrowRight class="inline-block w-4 h-4 ml-1 group-hover:translate-x-1 transition-all" />
        </div>
      </a>
      <div class="flex flex-col gap-5 h-full">
        <!-- Sexual Health Card -->
        <a href="#"
          class="relative rounded-2xl p-8 min-h-[250px] flex flex-col bg-gradient-to-br from-[#e7eafc] via-[#f0f3fd] to-[#eaf3fb] overflow-hidden group hover:shadow-md transition-all duration-300 border border-blue-100">
          <h3 class="text-blue-800 text-2xl font-bold mb-2">Sexual health</h3>
          <p class="text-blue-900 text-base mb-2">
            Regain your confidence and have the sexual life you've always wanted.
          </p>
          <div class="relative flex-1">
            <img src="@/assets/images/ed-pills.png" alt="Sexual Health"
              class="w-20 h-20 object-contain select-none pointer-events-none absolute left-0 bottom-0 lg:w-24 lg:h-24 lg:left-2 lg:-bottom-5 group-hover:scale-110 transition-transform duration-300" />
          </div>
          <div
            class="absolute right-8 bottom-8 bg-black text-white font-semibold px-5 py-2 rounded-full shadow hover:bg-gray-800 transition-all flex items-center z-10">
            Get Started
            <IconArrowRight class="inline-block w-4 h-4 ml-1 group-hover:translate-x-1 transition-all" />
          </div>
        </a>
        <!-- Hair Health Card -->
        <a href="#"
          class="relative rounded-2xl p-8 min-h-[250px] sm:min-h-[220px] flex flex-col bg-gradient-to-br from-[#e7fcf3] via-[#e6fdf3] to-[#eafbf7] overflow-hidden group hover:shadow-md transition-all duration-300 border border-green-100">
          <h3 class="text-green-800 text-2xl font-bold mb-2">Hair Health</h3>
          <p class="text-green-900 text-base mb-2">
            Address hair loss at the root with prescription medication.
          </p>
          <div class="relative flex-1">
            <img src="@/assets/images/hl-pills.png" alt="Hair Health"
              class="w-24 h-24 object-contain select-none pointer-events-none absolute -left-2 -bottom-2 lg:w-28 lg:h-28 lg:left-2 lg:-bottom-5 group-hover:scale-110 transition-transform duration-300" />
          </div>
          <div
            class="absolute right-8 bottom-8 bg-black text-white font-semibold px-5 py-2 rounded-full shadow hover:bg-gray-800 transition-all flex items-center z-10">
            Get Started
            <IconArrowRight class="inline-block w-4 h-4 ml-1 group-hover:translate-x-1 transition-all" />
          </div>
        </a>
      </div>
    </div>
  </section>
</template>

<style scoped>
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

@keyframes pulse-slow {

  0%,
  100% {
    opacity: 0.3;
  }

  50% {
    opacity: 0.5;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
