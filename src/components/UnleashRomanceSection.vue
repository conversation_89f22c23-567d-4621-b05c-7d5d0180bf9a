<script setup>
import { IconArrowRight } from "@tabler/icons-vue";
</script>

<template>
  <!-- Unleash Romance Section -->
  <section class="relative py-24 overflow-hidden max-w-7xl mx-auto sm:rounded-2xl mb-10">
    <div class="absolute inset-0 z-0">
      <img src="@/assets/images/unleash.jpg" alt="Romantic background" class="w-full h-full object-cover" />
      <div class="absolute inset-0 bg-red-900/40"></div>
    </div>
    <div class="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h2 class="text-5xl sm:text-6xl lg:text-7xl font-bold text-white mb-8 leading-tight">
        Unleash
        <br>
        your
        <br>
        romance.
      </h2>
      <div class="mt-12">
        <button
          class="bg-yellow-300 hover:bg-yellow-200 text-black font-semibold px-8 py-4 rounded-full text-lg transition-all duration-300 transform hover:scale-105 shadow-lg inline-flex items-center cursor-pointer">
          Get Your Meds
          <IconArrowRight class="w-5 h-5 ml-2" />
        </button>
      </div>
    </div>
  </section>
</template>
